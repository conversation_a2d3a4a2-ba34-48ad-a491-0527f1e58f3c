# =============================================================================
# models/sale_order.py
# =============================================================================
from odoo import models, fields, api
import base64
from datetime import date
import datetime
import io
import json
try:
    import xlsxwriter
except ImportError:
    xlsxwriter = None

class SaleOrder(models.Model):
    _inherit = 'sale.order'
    ro_commition_perc = fields.Float(string="Commission %")
    ro_actual_claim = fields.Float(string="Actual Claim")
    ro_clearance_value = fields.Float(string="Clearance Value")
    ro_freight_rate = fields.Float(string="Freight Rate")

    def _get_weeks_data(self, start_date, end_date):
        start = datetime.datetime(2025, 5, 2)
        end = datetime.datetime(2025, 8, 30)

        first_week = []
        remaining = []

        weeks = []
        weeks_e_days = []
        for i in range((end_date - start_date).days + 1): 
            current_date = start_date + datetime.timedelta(days=i)
            day_name = current_date.strftime('%A')
            
            # Fill the first_week until we hit Sunday
            if not first_week or first_week[-1].strftime('%A') != 'Sunday':
                first_week.append(current_date)
            else:
                remaining.append(current_date)

        # If the first_week did not end with Sunday, adjust the split
        if first_week and first_week[-1].strftime('%A') != 'Sunday':
            # Move remaining days to 'remaining' list starting from the next Monday
            while first_week and first_week[-1].strftime('%A') != 'Sunday':
                if len(first_week) > 0:
                    moved_date = first_week.pop()
                    remaining.insert(0, moved_date)

        
        if len(first_week) > 0:
            weeks.append(first_week[0])
            weeks_e_days.append(first_week[len(first_week)-1])
        for i in range((remaining[len(remaining)-1] - remaining[0]).days // 7 + 1): 
            current_date = remaining[0] + datetime.timedelta(days=i*7)
            day_name = current_date.strftime('%A')
            weeks.append(current_date)
            if(current_date+datetime.timedelta(days=6) <= remaining[len(remaining)-1]):
                weeks_e_days.append(current_date+datetime.timedelta(days=6))
            else: 
                weeks_e_days.append(remaining[len(remaining)-1])
            print(f"{current_date} - {day_name}")
        data = {"weeks_f_days": weeks, 'weeks_e_days': weeks_e_days, "end_date": remaining[len(remaining)-1] if (len(remaining) > 0) else first_week[len(first_week)-1] }
        return data
   
    def _get_weekly_sheet_data(self, start_date, end_date):
            
        total_grapes_net_weight = 0
        total_oranges_net_weight = 0
        total_dates_net_weight = 0
        total_olives_net_weight = 0
        total_potatoes_net_weight = 0
        total_vegetables_net_weight = 0


        total_grapes_invoice = 0
        total_oranges_invoice = 0
        total_dates_invoice = 0
        total_olives_invoice = 0
        total_potatoes_invoice = 0
        total_vegetables_invoice = 0


        total_grapes_net_fob = 0
        total_oranges_net_fob = 0
        total_dates_net_fob = 0
        total_olives_net_fob = 0
        total_potatoes_net_fob = 0
        total_vegetables_net_fob = 0
        
        for order in self.env['sale.order'].search([('date_order', '>=', start_date), ('date_order', '<=', end_date), ('state', 'in', ['sale'])]):
            for order_line in order.order_line:
                product_id = order_line.product_id
                total_invoice = order_line.price_total
                commission_value = order.ro_commition_perc * total_invoice
                freight_rate = order.ro_freight_rate
                clearance_value = order.ro_clearance_value
                actual_claim = order.ro_actual_claim
                
                total_deduction = commission_value + freight_rate + clearance_value + actual_claim
                total_net_fob = total_invoice - total_deduction
                print('anas')
                print(product_id.categ_id.ro_category_type)
                if product_id.categ_id.ro_category_type == 'oranges':
                    total_oranges_invoice += order_line.price_total
                    # total_oranges_net_weight += order_line.product_uom_qty 
                    total_oranges_net_fob += total_net_fob
                    total_oranges_net_weight += order_line.ro_computed_net_weight 
                if product_id.categ_id.ro_category_type == 'grapes':
                    total_grapes_invoice += order_line.price_total
                    # total_grapes_net_weight += order_line.product_uom_qty
                    total_grapes_net_fob += total_net_fob
                    total_grapes_net_weight += order_line.ro_computed_net_weight
                if product_id.categ_id.ro_category_type == 'olives':
                    total_olives_invoice += order_line.price_total
                    # total_olives_net_weight += order_line.product_uom_qty
                    total_olives_net_fob += total_net_fob
                    total_olives_net_weight += order_line.ro_computed_net_weight
                if product_id.categ_id.ro_category_type == 'dates':
                    total_dates_invoice += order_line.price_total
                    # total_dates_net_weight += order_line.product_uom_qty
                    total_dates_net_fob += total_net_fob
                    total_dates_net_weight += order_line.ro_computed_net_weight
                if product_id.categ_id.ro_category_type == 'potatoes':
                    total_potatoes_invoice += order_line.price_total
                    total_potatoes_net_fob += total_net_fob
                    total_potatoes_net_weight += order_line.ro_computed_net_weight
                if product_id.categ_id.ro_category_type == 'vegetables':
                    total_vegetables_invoice += order_line.price_total
                    total_vegetables_net_fob += total_net_fob
                    total_vegetables_net_weight += order_line.ro_computed_net_weight
            
        return {
            "total_grapes_net_weight":  total_grapes_net_weight, 
            "total_oranges_net_weight": total_oranges_net_weight, 
            "total_dates_net_weight":   total_dates_net_weight, 
            "total_olives_net_weight":  total_olives_net_weight, 
            "total_grapes_invoice":     total_grapes_invoice, 
            "total_oranges_invoice":    total_oranges_invoice, 
            "total_dates_invoice":      total_dates_invoice, 
            "total_olives_invoice":     total_olives_invoice, 
            "total_grapes_net_fob":     total_grapes_net_fob, 
            "total_oranges_net_fob":    total_oranges_net_fob, 
            "total_dates_net_fob":      total_dates_net_fob, 
            "total_olives_net_fob":     total_olives_net_fob, 
        } 
   
    def action_yearly_sale_excel_export(self, from_date, to_date):
        """Export multiple sale orders to Excel"""
        if not xlsxwriter:
            raise UserError("Please install xlsxwriter: pip install xlsxwriter")
        
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        

        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 18,
            'align': 'center',
            'valign': 'vcenter',
            'border': 1,
            'underline': 1,
        })
        
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#9dffad',
            'border': 1,
        })
        
        normal_format = workbook.add_format({
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
        })
        
        normal_dollar_format = workbook.add_format({
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': '$#,##0',
        })
        
        orange_header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'bg_color': 'orange',
            'valign': 'vcenter',
            'border': 1
        })
        
        olive_header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'bg_color': '#00c43b',
            'valign': 'vcenter',
            'border': 1
        })
        
        date_header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#ea9c9c',
            'border': 1
        })
        
        grape_header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'bg_color': 'yellow',
            'valign': 'vcenter',
            'border': 1
        })
        
        totals_header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'bg_color': '#dddddd',
            'valign': 'vcenter',
            'border': 1
        })
        
        dollar_currency_format = workbook.add_format({
            'num_format': '$#,##0',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        dollar_totals_currency_format = workbook.add_format({
            'num_format': '$#,##0',
            'bg_color': '#dddddd',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
        })
        
        
        # Create summary worksheet
        worksheet = workbook.add_worksheet('Sales Orders Summary')
        workbook.set_calc_mode('auto')
        # Set row widths
        worksheet.set_row(1, 50)
        worksheet.set_row(2, 20)
        worksheet.set_row(4, 25)
        worksheet.set_row(5, 25)
        worksheet.set_row(6, 25)
        worksheet.set_row(7, 25)
        worksheet.set_row(8, 25)
        
        # Set column widths
        worksheet.set_column('A:A', 20)
        worksheet.set_column('B:B', 20)
        worksheet.set_column('C:C', 20)
        worksheet.set_column('D:D', 20)
        worksheet.set_column('E:E', 20)
        worksheet.set_column('F:F', 20)
        
        # Headers
        headers = ['Total Volume', 'Invoice Value USD', 'Return plus claims', 'Net Value FOB /USD', 'FOB return/Ton USD']
        for col, header in enumerate(headers):
            worksheet.write(2, col+1, header, header_format)
            
            
        product_types = ['date', 'orange', 'grape', 'olive']
        n = len(product_types)
        first = 4  
        last = first + n - 1
        for i, _ in enumerate(product_types):
            # worksheet.write_formula(i+4, 5, f'=E{i+5}/B{i+5}', dollar_currency_format)
            worksheet.write_array_formula(first, 5,
                             last, 5,
                             f'{{=E{first+1}:E{last+1}/B{first+1}:B{last+1}}}',
                             dollar_currency_format)

        total_grapes_net_weight = 0
        total_oranges_net_weight = 0
        total_dates_net_weight = 0
        total_olives_net_weight = 0
        
        
        total_grapes_invoice = 0
        total_oranges_invoice = 0
        total_dates_invoice = 0
        total_olives_invoice = 0
        
        
        total_grapes_net_fob = 0
        total_oranges_net_fob = 0
        total_dates_net_fob = 0
        total_olives_net_fob = 0

    
        for order in self.env['sale.order'].search([('date_order', '>=', from_date), ('date_order', '<=', to_date), ('state', 'in', ['sale'])]):
            for order_line in order.order_line:
                product_id = order_line.product_id
                total_invoice = order_line.price_total
                commission_value = order.ro_commition_perc * total_invoice
                freight_rate = order.ro_freight_rate
                clearance_value = order.ro_clearance_value
                actual_claim = order.ro_actual_claim
                
                total_deduction = commission_value + freight_rate + clearance_value + actual_claim
                total_net_fob = total_invoice - total_deduction
                if product_id.categ_id.ro_category_type == 'oranges':
                    total_oranges_invoice += order_line.price_total
                    # total_oranges_net_weight += order_line.product_uom_qty 
                    total_oranges_net_fob += total_net_fob
                    total_oranges_net_weight += order_line.ro_computed_net_weight 
                if product_id.categ_id.ro_category_type == 'grapes':
                    total_grapes_invoice += order_line.price_total
                    # total_grapes_net_weight += order_line.product_uom_qty
                    total_grapes_net_fob += total_net_fob
                    total_grapes_net_weight += order_line.ro_computed_net_weight
                if product_id.categ_id.ro_category_type == 'olives':
                    total_olives_invoice += order_line.price_total
                    # total_olives_net_weight += order_line.product_uom_qty
                    total_olives_net_fob += total_net_fob
                    total_olives_net_weight += order_line.ro_computed_net_weight
                if product_id.categ_id.ro_category_type == 'dates':
                    total_dates_invoice += order_line.price_total
                    # total_dates_net_weight += order_line.product_uom_qty
                    total_dates_net_fob += total_net_fob
                    total_dates_net_weight += order_line.ro_computed_net_weight
        
        title = f"Xeed Export Season {from_date.year}-{from_date.year+1}"
        # worksheet.write(1, 0, title, title_format)
        worksheet.merge_range(1, 1, 1, 4, title, title_format)
        
        # write the headers
        worksheet.write(4, 0, 'Dates', date_header_format)   
        worksheet.write(5, 0, 'Oranges', orange_header_format)   
        worksheet.write(6, 0, 'Grapes', grape_header_format)   
        worksheet.write(7, 0, 'Olives', olive_header_format)   
        worksheet.write(8, 0, 'Total', totals_header_format)   
        
        # write the net weights
        worksheet.write(4, 1, total_dates_net_weight, normal_format)   
        worksheet.write(5, 1, total_oranges_net_weight, normal_format)   
        worksheet.write(6, 1, total_grapes_net_weight, normal_format)   
        worksheet.write(7, 1, total_olives_net_weight, normal_format)   
        
        
        # write the invoice
        worksheet.write(4, 2, total_dates_invoice, normal_dollar_format)   
        worksheet.write(5, 2, total_oranges_invoice, normal_dollar_format)   
        worksheet.write(6, 2, total_grapes_invoice, normal_dollar_format)   
        worksheet.write(7, 2, total_olives_invoice, normal_dollar_format)   
        
        # write the net fob
        worksheet.write(4, 4, total_dates_net_fob, normal_dollar_format)   
        worksheet.write(5, 4, total_oranges_net_fob, normal_dollar_format)   
        worksheet.write(6, 4, total_grapes_net_fob, normal_dollar_format)   
        worksheet.write(7, 4, total_olives_net_fob, normal_dollar_format)   

        # setting the totals fields
        worksheet.write_array_formula('B9', '=SUM(B2:B8)', totals_header_format)
        worksheet.write_array_formula('C9', '=SUM(C5:C8)', dollar_totals_currency_format)
        worksheet.write_array_formula('D9', '=SUM(D5:D8)', dollar_totals_currency_format)
        worksheet.write_array_formula('E9', '=SUM(E5:E8)', dollar_totals_currency_format)
        worksheet.write_array_formula('F9', '=E9/B9', dollar_totals_currency_format)
        
        workbook.close()
        output.seek(0)
        
        # Create attachment
        filename = f'sales_orders_export_{fields.Datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(output.read()),
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }
    
    def action_sale_excel_export(self):
        # wizard = self.env['period.selection.wizard'].create({})   
        return {
            'name': 'Choose Excel Sheet Type',
            'type': 'ir.actions.act_window',
            'res_model': 'period.selection.wizard',  
            'view_mode': 'form', 
            'context': {
                'default_sale_order_id': self.id  # passing the sale order id to wizard
            },
            'target': 'new',  
        }
    
    def action_weekly_sale_excel_export(self, from_date, to_date):
        """Export multiple sale orders to Excel"""
        if not xlsxwriter:
            raise UserError("Please install xlsxwriter: pip install xlsxwriter")
        
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        
        
        cell_format_dict = {
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
        }
        
        
        # Define formats
        title_format_dict = {**cell_format_dict, 'bold': True, 'font_size': 12}
        
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#9dffad',
            'border': 1,
        })
        
        
        normal_format = workbook.add_format({
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
        })
        
        normal_dollar_format = workbook.add_format({
            'font_size': 12,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': '$#,##0',
        })
        
        
        orange_header_format = workbook.add_format({**title_format_dict, 'bg_color': 'orange'})
        olive_header_format = workbook.add_format({**title_format_dict, 'bg_color': '#00c43b'})
        date_header_format = workbook.add_format({**title_format_dict, 'bg_color': '#ea9c9c'})
        grape_header_format = workbook.add_format({**title_format_dict, 'bg_color': 'yellow'})
        grey_header_format = workbook.add_format({**title_format_dict, 'bg_color': '#dddddd'}) 
        
        totals_header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'align': 'center',
            'bg_color': '#dddddd',
            'valign': 'vcenter',
            'border': 1
        })
        # Define currency format (e.g., $20.00)
        dollar_currency_format = workbook.add_format({
            'num_format': '$#,##0',
            'align': 'center',
            'valign': 'vcenter',
            'border': 1
        })
        
        dollar_totals_currency_format = workbook.add_format({
            'num_format': '$#,##0',
            'bg_color': '#dddddd',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
        })
        
        
        # Create summary worksheet
        worksheet = workbook.add_worksheet('Sales Weekly Summary')
        
        # Set column widths
        worksheet.set_column('A:A', 15)
        worksheet.set_column('B:B', 13)
        worksheet.set_column('C:C', 18)
        worksheet.set_column('D:D', 30)
        worksheet.set_column('E:E', 18)
        worksheet.set_column('F:F', 13)
        worksheet.set_column('G:G', 18)
        worksheet.set_column('H:H', 30)
        worksheet.set_column('I:I', 18)
        worksheet.set_column('J:J', 13)
        worksheet.set_column('K:K', 18)
        worksheet.set_column('L:L', 30)
        worksheet.set_column('M:M', 18)
        worksheet.set_column('N:N', 13)
        worksheet.set_column('O:O', 18)
        worksheet.set_column('P:P', 30)
        worksheet.set_column('Q:Q', 18)

        worksheet.merge_range(0, 0, 1, 0, 'Weeks', grey_header_format)
        
        worksheet.merge_range(0, 1, 0, 4, 'Oranges', orange_header_format)
        worksheet.merge_range(0, 5, 0, 8, 'Grapes', grape_header_format)
        worksheet.merge_range(0, 9, 0, 12, 'Olives', olive_header_format)
        worksheet.merge_range(0, 13, 0, 16, 'Dates', date_header_format)
        
        worksheet.write(1, 1, 'Total Volume', orange_header_format)
        worksheet.write(1, 2, 'Invoice Value USD', orange_header_format)
        worksheet.write(1, 3, 'Return after claims and rebate', orange_header_format)
        worksheet.write(1, 4, 'Value FOB /USD', orange_header_format)
        
        worksheet.write(1, 5, 'Total Volume', grape_header_format)
        worksheet.write(1, 6, 'Invoice Value USD', grape_header_format)
        worksheet.write(1, 7, 'Return after claims and rebate', grape_header_format)
        worksheet.write(1, 8, 'Value FOB', grape_header_format)
        
        worksheet.write(1, 9, 'Total Volume', olive_header_format)
        worksheet.write(1, 10, 'Invoice Value USD', olive_header_format)
        worksheet.write(1, 11, 'Return after claims and rebate', olive_header_format)
        worksheet.write(1, 12, 'Value FOB', olive_header_format)
        
        worksheet.write(1, 13, 'Total Volume', date_header_format)
        worksheet.write(1, 14, 'Invoice Value USD', date_header_format)
        worksheet.write(1, 15, 'Return after claims and rebate', date_header_format)
        worksheet.write(1, 16, 'Value FOB', date_header_format)
        
        weeks_data = self._get_weeks_data(from_date, to_date)
        
        for i, start_date in enumerate(weeks_data["weeks_f_days"]):
            end_date = weeks_data["weeks_e_days"][i]
            worksheet.write(i+2, 0, f'week {i+1}', normal_format)
            full_week_data = self._get_weekly_sheet_data(start_date, end_date)

            worksheet.write(i+2, 1, full_week_data['total_oranges_net_weight'], normal_format)
            worksheet.write(i+2, 2, full_week_data['total_oranges_invoice'], normal_dollar_format)
            worksheet.write(i+2, 4, full_week_data['total_oranges_net_fob'], normal_dollar_format)
            
            worksheet.write(i+2, 5, full_week_data['total_grapes_net_weight'], normal_format)
            worksheet.write(i+2, 6, full_week_data['total_grapes_invoice'], normal_dollar_format)
            worksheet.write(i+2, 8, full_week_data['total_grapes_net_fob'], normal_dollar_format)
            
            worksheet.write(i+2, 9, full_week_data['total_olives_net_weight'], normal_format)
            worksheet.write(i+2, 10, full_week_data['total_olives_invoice'], normal_dollar_format)
            worksheet.write(i+2, 12, full_week_data['total_olives_net_fob'], normal_dollar_format)
            
            worksheet.write(i+2, 13, full_week_data['total_dates_net_weight'], normal_format)
            worksheet.write(i+2, 14, full_week_data['total_dates_invoice'], normal_dollar_format)
            worksheet.write(i+2, 16, full_week_data['total_dates_net_fob'], normal_dollar_format)

            
        total_index = len(weeks_data["weeks_f_days"]) + 2
        
        worksheet.write(total_index, 0, 'Total', totals_header_format)
        
        # setting the totals fields
        worksheet.write_array_formula(f'B{total_index+1}', f'=SUM(B3:B{total_index})', totals_header_format)
        worksheet.write_array_formula(f'C{total_index+1}', f'=SUM(C3:C{total_index})', dollar_totals_currency_format)
        worksheet.write_array_formula(f'D{total_index+1}', f'=SUM(D3:D{total_index})', totals_header_format)
        worksheet.write_array_formula(f'E{total_index+1}', f'=SUM(E3:E{total_index})', dollar_totals_currency_format)
        
        worksheet.write_array_formula(f'F{total_index+1}', f'=SUM(F3:F{total_index})', totals_header_format)
        worksheet.write_array_formula(f'G{total_index+1}', f'=SUM(G3:G{total_index})', dollar_totals_currency_format)
        worksheet.write_array_formula(f'H{total_index+1}', f'=SUM(H3:H{total_index})', totals_header_format)
        worksheet.write_array_formula(f'I{total_index+1}', f'=SUM(I3:I{total_index})', dollar_totals_currency_format)
        
        worksheet.write_array_formula(f'J{total_index+1}', f'=SUM(J3:J{total_index})', totals_header_format)
        worksheet.write_array_formula(f'K{total_index+1}', f'=SUM(K3:K{total_index})', dollar_totals_currency_format)
        worksheet.write_array_formula(f'L{total_index+1}', f'=SUM(L3:L{total_index})', totals_header_format)
        worksheet.write_array_formula(f'M{total_index+1}', f'=SUM(M3:M{total_index})', dollar_totals_currency_format)
        
        worksheet.write_array_formula(f'N{total_index+1}', f'=SUM(N3:N{total_index})', totals_header_format)
        worksheet.write_array_formula(f'O{total_index+1}', f'=SUM(O3:O{total_index})', dollar_totals_currency_format)
        worksheet.write_array_formula(f'P{total_index+1}', f'=SUM(P3:P{total_index})', totals_header_format)
        worksheet.write_array_formula(f'Q{total_index+1}', f'=SUM(Q3:Q{total_index})', dollar_totals_currency_format)

        workbook.close()
        output.seek(0)
        
        # Create attachment
        filename = f'sales_orders_export_{fields.Datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(output.read()),
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }
        
        
